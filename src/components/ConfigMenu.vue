<template>
  <q-dialog v-model="show">
    <q-card class="config-menu">
      <q-card-section>
        <div class="text-h6">Configuration</div>
      </q-card-section>

      <!-- Select number of streams -->
      <q-card-section>
        <q-select
          v-model="streamCount"
          :options="streamCountOptions"
          label="Number of Streams"
          dense
          outlined
        />
      </q-card-section>

      <!-- List of channel selectors -->
      <q-card-section>
        <q-list>
          <q-item v-for="(item, index) in streamCount" :key="index">
            <q-item-section>
              <q-select
                v-model="selectedStreams[index]"
                :options="streamOptions"
                :label="'Select Channel for Stream ' + (index + 1)"
                emit-value
                map-options
                option-label="label"
                option-value="value"
                dense
                outlined
                @update:model-value="updateStream(index)"
              />
              <q-input
                v-if="selectedStreams[index] === 'custom'"
                v-model="customUrls[index]"
                label="Enter Custom URL"
                dense
                filled
              />
              <div class="q-mt-sm">
                <q-toggle
                  v-model="mutedStreams[index]"
                  label="Mute"
                  color="primary"
                />
              </div>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn label="Cancel" color="grey" v-close-popup />
        <q-btn label="Apply" color="primary" @click="applyConfig" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script>
import { ref, watch, onMounted } from 'vue'

export default {
  name: 'ConfigMenu',
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    currentStreams: {
      type: Array,
      default: () => [],
    },
    currentMutedState: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['update:modelValue', 'updateConfig'],
  setup(props, { emit }) {
    const show = ref(props.modelValue)
    const streamCountOptions = ref([1, 2, 4, 5, 6, 9])
    // Initialize streamCount based on the current streams length
    const streamCount = ref(props.currentStreams.length || 1)

    const availableStreams = ref([])
    const streamOptions = ref([])

    const selectedStreams = ref([])
    const customUrls = ref([])
    const mutedStreams = ref([])

    // Load available channels from the online JSON file
    onMounted(async () => {
      try {
        const response = await fetch(
          'https://trleahy.github.io/API-Endpoints/visorChannels/channels.json',
        )
        const data = await response.json()
        availableStreams.value = Object.keys(data).map((key) => {
          return { label: data[key].name, value: data[key].url }
        })
        streamOptions.value = [...availableStreams.value, { label: 'Custom URL', value: 'custom' }]
      } catch (err) {
        console.error('Failed to load channels:', err)
      }
      initializeStreams()
    })

    const initializeStreams = () => {
      selectedStreams.value = []
      customUrls.value = []
      mutedStreams.value = []
      
      for (let i = 0; i < streamCount.value; i++) {
        selectedStreams.value.push(streamOptions.value[0] ? streamOptions.value[0].value : 'custom')
        customUrls.value.push('')
        // Initialize muted state from props if available, otherwise default to true (muted)
        mutedStreams.value.push(props.currentMutedState[i] !== undefined ? props.currentMutedState[i] : true)
      }
    }

    watch(streamCount, (newCount, oldCount) => {
      if (newCount > oldCount) {
        for (let i = oldCount; i < newCount; i++) {
          selectedStreams.value.push(
            streamOptions.value[0] ? streamOptions.value[0].value : 'custom',
          )
          customUrls.value.push('')
          mutedStreams.value.push(true) // Default new streams to muted
        }
      } else if (newCount < oldCount) {
        selectedStreams.value.splice(newCount)
        customUrls.value.splice(newCount)
        mutedStreams.value.splice(newCount)
      }
    })

    watch(
      () => props.modelValue,
      (newVal) => {
        show.value = newVal
      },
    )
    watch(show, (newVal) => {
      emit('update:modelValue', newVal)
    })

    const updateStream = (index) => {
      if (selectedStreams.value[index] !== 'custom') {
        customUrls.value[index] = ''
      }
    }

    const applyConfig = () => {
      const finalStreams = selectedStreams.value.map((stream, index) => {
        return stream === 'custom' ? customUrls.value[index] : stream
      })
      emit('updateConfig', { streams: finalStreams, mutedState: mutedStreams.value })
      show.value = false
    }

    return {
      show,
      streamCountOptions,
      streamCount,
      streamOptions,
      selectedStreams,
      customUrls,
      mutedStreams,
      updateStream,
      applyConfig,
    }
  },
}
</script>

<style scoped>
.config-menu {
  width: 400px;
}
</style>
