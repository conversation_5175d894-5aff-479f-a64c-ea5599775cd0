<template>
  <q-page class="flex flex-center">
    <!-- Grid container using containerStyle -->
    <div class="grid-container" :style="containerStyle">
      <!-- Each video wrapper gets its grid area via getItemStyle -->
      <div
        v-for="(url, index) in streamUrls"
        :key="index"
        class="video-wrapper"
        :style="getItemStyle(index)"
      >
        <video class="video-player" autoplay :muted="mutedState[index]"></video>
      </div>
    </div>

    <!-- Configuration menu -->
    <ConfigMenu
      v-model="showConfigMenu"
      :currentStreams="streamUrls"
      :currentMutedState="mutedState"
      @updateConfig="updateConfig"
    />
  </q-page>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import Hls from 'hls.js'
import ConfigMenu from 'components/ConfigMenu.vue'

export default {
  name: 'IndexPage',
  components: { ConfigMenu },
  setup() {
    const DEFAULT_URL =
      'https://vs-hls-push-ww-live.akamaized.net/x=4/i=urn:bbc:pips:service:bbc_news_channel_hd/t=3840/v=pv10/b=1604032/main.m3u8'
    // Default to 2 streams
    const streamUrls = ref(Array(2).fill(DEFAULT_URL))
    const mutedState = ref(Array(2).fill(true)) // Default all streams to muted
    const showConfigMenu = ref(false)

    // containerStyle returns the grid container style based on number of streams.
    const containerStyle = computed(() => {
      const num = streamUrls.value.length
      switch (num) {
        case 1:
          return {
            display: 'grid',
            gridTemplateColumns: '1fr',
            gridTemplateRows: '1fr',
            gap: '0px',
            width: '100vw',
            height: '100vh',
          }
        case 2:
          return {
            display: 'grid',
            gridTemplateColumns: 'repeat(2, 1fr)',
            gridTemplateRows: '1fr',
            gap: '0px',
            width: '100vw',
            height: '100vh',
          }
        case 4:
          return {
            display: 'grid',
            gridTemplateColumns: 'repeat(2, 1fr)',
            gridTemplateRows: 'repeat(2, 1fr)',
            gap: '0px',
            width: '100vw',
            height: '100vh',
          }
        case 5:
          return {
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gridTemplateRows: 'repeat(4, 1fr)',
            gap: '0px',
            width: '100vw',
            height: '100vh',
          }
        case 6:
          return {
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gridTemplateRows: 'repeat(3, 1fr)',
            gap: '0px',
            width: '100vw',
            height: '100vh',
          }
        case 9:
          return {
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gridTemplateRows: 'repeat(3, 1fr)',
            gap: '0px',
            width: '100vw',
            height: '100vh',
          }
        default: {
          const cols = Math.ceil(Math.sqrt(num))
          const rows = Math.ceil(num / cols)
          return {
            display: 'grid',
            gridTemplateColumns: `repeat(${cols}, 1fr)`,
            gridTemplateRows: `repeat(${rows}, 1fr)`,
            gap: '0px',
            width: '100vw',
            height: '100vh',
          }
        }
      }
    })

    // getItemStyle returns the grid-area for each stream, matching your generator.
    const getItemStyle = (index) => {
      const num = streamUrls.value.length
      if (num === 1) {
        return { gridArea: '1 / 1 / 2 / 2' }
      } else if (num === 2) {
        return index === 0 ? { gridArea: '1 / 1 / 2 / 2' } : { gridArea: '1 / 2 / 2 / 3' }
      } else if (num === 4) {
        switch (index) {
          case 0:
            return { gridArea: '1 / 1 / 2 / 2' }
          case 1:
            return { gridArea: '1 / 2 / 2 / 3' }
          case 2:
            return { gridArea: '2 / 1 / 3 / 2' }
          case 3:
            return { gridArea: '2 / 2 / 3 / 3' }
          default:
            return {}
        }
      } else if (num === 5) {
        // Five streams layout:
        // .div1: 1 / 1 / 5 / 3;
        // .div2: 1 / 3 / 2 / 4;
        // .div3: 2 / 3 / 3 / 4;
        // .div4: 3 / 3 / 4 / 4;
        // .div5: 4 / 3 / 5 / 4;
        switch (index) {
          case 0:
            return { gridArea: '1 / 1 / 5 / 3' }
          case 1:
            return { gridArea: '1 / 3 / 2 / 4' }
          case 2:
            return { gridArea: '2 / 3 / 3 / 4' }
          case 3:
            return { gridArea: '3 / 3 / 4 / 4' }
          case 4:
            return { gridArea: '4 / 3 / 5 / 4' }
          default:
            return {}
        }
      } else if (num === 6) {
        // Six streams layout:
        // .div1: 1 / 1 / 3 / 3;
        // .div2: 3 / 1 / 4 / 2;
        // .div3: 3 / 2 / 4 / 3;
        // .div4: 3 / 3 / 4 / 4;
        // .div5: 2 / 3 / 3 / 4;
        // .div6: 1 / 3 / 2 / 4;
        switch (index) {
          case 0:
            return { gridArea: '1 / 1 / 3 / 3' }
          case 1:
            return { gridArea: '3 / 1 / 4 / 2' }
          case 2:
            return { gridArea: '3 / 2 / 4 / 3' }
          case 3:
            return { gridArea: '3 / 3 / 4 / 4' }
          case 4:
            return { gridArea: '2 / 3 / 3 / 4' }
          case 5:
            return { gridArea: '1 / 3 / 2 / 4' }
          default:
            return {}
        }
      } else if (num === 9) {
        // Nine streams layout:
        // .div1: 1 / 1 / 2 / 2;
        // .div2: 1 / 2 / 2 / 3;
        // .div3: 1 / 3 / 2 / 4;
        // .div4: 2 / 1 / 3 / 2;
        // .div5: 2 / 2 / 3 / 3;
        // .div6: 2 / 3 / 3 / 4;
        // .div7: 3 / 1 / 4 / 2;
        // .div8: 3 / 2 / 4 / 3;
        // .div9: 3 / 3 / 4 / 4;
        switch (index) {
          case 0:
            return { gridArea: '1 / 1 / 2 / 2' }
          case 1:
            return { gridArea: '1 / 2 / 2 / 3' }
          case 2:
            return { gridArea: '1 / 3 / 2 / 4' }
          case 3:
            return { gridArea: '2 / 1 / 3 / 2' }
          case 4:
            return { gridArea: '2 / 2 / 3 / 3' }
          case 5:
            return { gridArea: '2 / 3 / 3 / 4' }
          case 6:
            return { gridArea: '3 / 1 / 4 / 2' }
          case 7:
            return { gridArea: '3 / 2 / 4 / 3' }
          case 8:
            return { gridArea: '3 / 3 / 4 / 4' }
          default:
            return {}
        }
      }
      // Default: no explicit positioning (will flow normally)
      return {}
    }

    // Attach HLS streams to video elements using Hls.js (if supported)
    const attachStreams = () => {
      const videos = document.querySelectorAll('.video-player')
      videos.forEach((video, index) => {
        const streamUrl = streamUrls.value[index] || DEFAULT_URL
        if (Hls.isSupported()) {
          const hls = new Hls()
          hls.loadSource(streamUrl)
          hls.attachMedia(video)
        } else {
          video.src = streamUrl
        }
        // Set muted state
        video.muted = mutedState.value[index]
      })
    }

    // Listen for F2 key press to open the config menu
    const handleKeyDown = (event) => {
      if (event.key === 'F2') {
        showConfigMenu.value = true
      }
    }

    onMounted(() => {
      nextTick(() => {
        attachStreams()
      })
      window.addEventListener('keydown', handleKeyDown)
    })

    onUnmounted(() => {
      window.removeEventListener('keydown', handleKeyDown)
    })

    watch(streamUrls, () => {
      nextTick(() => {
        attachStreams()
      })
    })

    watch(mutedState, () => {
      nextTick(() => {
        const videos = document.querySelectorAll('.video-player')
        videos.forEach((video, index) => {
          video.muted = mutedState.value[index]
        })
      })
    })

    const updateConfig = (config) => {
      streamUrls.value = config.streams
      mutedState.value = config.mutedState
    }

    return {
      streamUrls,
      mutedState,
      showConfigMenu,
      containerStyle,
      getItemStyle,
      updateConfig,
    }
  },
}
</script>

<style scoped>
.grid-container {
  width: 100vw;
  height: 100vh;
  background: #000;
}
.video-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}
.video-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain; /* letterboxes to maintain aspect ratio */
  object-position: center;
  background: black;
}
</style>
