<template>
  <q-page class="flex flex-center">
    <!-- Grid container using containerStyle -->
    <div class="grid-container" :style="containerStyle">
      <!-- Each video wrapper gets its grid area via getItemStyle -->
      <div
        v-for="(_, index) in streamUrls"
        :key="index"
        class="video-wrapper"
        :style="getItemStyle(index)"
      >
        <video class="video-player" autoplay :muted="mutedState[index]"></video>
      </div>
    </div>

    <!-- Configuration menu -->
    <ConfigMenu
      v-model="showConfigMenu"
      :currentStreams="streamUrls"
      :currentMutedState="mutedState"
      @updateConfig="updateConfig"
    />
  </q-page>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import Hls from 'hls.js'
import ConfigMenu from 'components/ConfigMenu.vue'

export default {
  name: 'IndexPage',
  components: { ConfigMenu },
  setup() {
    const DEFAULT_URL =
      'https://vs-hls-push-ww-live.akamaized.net/x=4/i=urn:bbc:pips:service:bbc_news_channel_hd/t=3840/v=pv10/b=1604032/main.m3u8'

    // Reactive state
    const streamUrls = ref(Array(2).fill(DEFAULT_URL))
    const mutedState = ref(Array(2).fill(true)) // Default all streams to muted
    const showConfigMenu = ref(false)
    const hlsInstances = ref([]) // Track HLS instances for cleanup

    // Grid layout configurations
    const GRID_LAYOUTS = {
      1: { columns: '1fr', rows: '1fr' },
      2: { columns: 'repeat(2, 1fr)', rows: '1fr' },
      4: { columns: 'repeat(2, 1fr)', rows: 'repeat(2, 1fr)' },
      5: { columns: 'repeat(3, 1fr)', rows: 'repeat(4, 1fr)' },
      6: { columns: 'repeat(3, 1fr)', rows: 'repeat(3, 1fr)' },
      9: { columns: 'repeat(3, 1fr)', rows: 'repeat(3, 1fr)' }
    }

    // Base grid style properties
    const BASE_GRID_STYLE = {
      display: 'grid',
      gap: '0px',
      width: '100vw',
      height: '100vh'
    }

    // Computed grid container style
    const containerStyle = computed(() => {
      const num = streamUrls.value.length
      const layout = GRID_LAYOUTS[num]

      if (layout) {
        return {
          ...BASE_GRID_STYLE,
          gridTemplateColumns: layout.columns,
          gridTemplateRows: layout.rows
        }
      }

      // Default layout for other numbers
      const cols = Math.ceil(Math.sqrt(num))
      const rows = Math.ceil(num / cols)
      return {
        ...BASE_GRID_STYLE,
        gridTemplateColumns: `repeat(${cols}, 1fr)`,
        gridTemplateRows: `repeat(${rows}, 1fr)`
      }
    })

    // Grid area configurations for specific layouts
    const GRID_AREAS = {
      1: ['1 / 1 / 2 / 2'],
      2: ['1 / 1 / 2 / 2', '1 / 2 / 2 / 3'],
      4: [
        '1 / 1 / 2 / 2', '1 / 2 / 2 / 3',
        '2 / 1 / 3 / 2', '2 / 2 / 3 / 3'
      ],
      5: [
        '1 / 1 / 5 / 3', // Large left panel
        '1 / 3 / 2 / 4', '2 / 3 / 3 / 4',
        '3 / 3 / 4 / 4', '4 / 3 / 5 / 4'
      ],
      6: [
        '1 / 1 / 3 / 3', // Large top-left panel
        '3 / 1 / 4 / 2', '3 / 2 / 4 / 3', '3 / 3 / 4 / 4',
        '2 / 3 / 3 / 4', '1 / 3 / 2 / 4'
      ],
      9: [
        '1 / 1 / 2 / 2', '1 / 2 / 2 / 3', '1 / 3 / 2 / 4',
        '2 / 1 / 3 / 2', '2 / 2 / 3 / 3', '2 / 3 / 3 / 4',
        '3 / 1 / 4 / 2', '3 / 2 / 4 / 3', '3 / 3 / 4 / 4'
      ]
    }

    // Get grid area style for a specific stream index
    const getItemStyle = (index) => {
      const num = streamUrls.value.length
      const areas = GRID_AREAS[num]

      if (areas && areas[index]) {
        return { gridArea: areas[index] }
      }

      // Default: no explicit positioning (will flow normally)
      return {}
    }

    // Clean up existing HLS instances
    const cleanupHlsInstances = () => {
      hlsInstances.value.forEach(hls => {
        if (hls) {
          hls.destroy()
        }
      })
      hlsInstances.value = []
    }

    // Attach HLS streams to video elements with proper error handling
    const attachStreams = () => {
      // Clean up existing instances first
      cleanupHlsInstances()

      const videos = document.querySelectorAll('.video-player')
      videos.forEach((video, index) => {
        const streamUrl = streamUrls.value[index] || DEFAULT_URL

        try {
          if (Hls.isSupported()) {
            const hls = new Hls({
              enableWorker: false, // Disable worker for better compatibility
              lowLatencyMode: true
            })

            hls.on(Hls.Events.ERROR, (_, data) => {
              console.error(`HLS Error for stream ${index}:`, data)
              if (data.fatal) {
                // Try to recover from fatal errors
                switch (data.type) {
                  case Hls.ErrorTypes.NETWORK_ERROR:
                    hls.startLoad()
                    break
                  case Hls.ErrorTypes.MEDIA_ERROR:
                    hls.recoverMediaError()
                    break
                  default:
                    hls.destroy()
                    break
                }
              }
            })

            hls.loadSource(streamUrl)
            hls.attachMedia(video)
            hlsInstances.value[index] = hls
          } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            // Native HLS support (Safari)
            video.src = streamUrl
          } else {
            console.warn(`HLS not supported for stream ${index}`)
            video.src = streamUrl // Fallback
          }

          // Set muted state
          video.muted = mutedState.value[index] ?? true
        } catch (error) {
          console.error(`Error attaching stream ${index}:`, error)
        }
      })
    }

    // Keyboard event handler for opening config menu
    const handleKeyDown = (event) => {
      if (event.key === 'F2') {
        showConfigMenu.value = true
      }
    }

    // Update muted state for all video elements
    const updateVideoMutedState = () => {
      const videos = document.querySelectorAll('.video-player')
      videos.forEach((video, index) => {
        if (video && mutedState.value[index] !== undefined) {
          video.muted = mutedState.value[index]
        }
      })
    }

    // Configuration update handler
    const updateConfig = (config) => {
      if (config.streams && Array.isArray(config.streams)) {
        streamUrls.value = config.streams
      }
      if (config.mutedState && Array.isArray(config.mutedState)) {
        mutedState.value = config.mutedState
      }
    }

    // Lifecycle hooks
    onMounted(() => {
      nextTick(() => {
        attachStreams()
      })
      window.addEventListener('keydown', handleKeyDown)
    })

    onUnmounted(() => {
      window.removeEventListener('keydown', handleKeyDown)
      cleanupHlsInstances()
    })

    // Watchers
    watch(streamUrls, () => {
      nextTick(() => {
        attachStreams()
      })
    }, { deep: true })

    watch(mutedState, () => {
      nextTick(() => {
        updateVideoMutedState()
      })
    }, { deep: true })

    return {
      streamUrls,
      mutedState,
      showConfigMenu,
      containerStyle,
      getItemStyle,
      updateConfig
    }
  },
}
</script>

<style scoped>
.grid-container {
  width: 100vw;
  height: 100vh;
  background: #000;
}
.video-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}
.video-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain; /* letterboxes to maintain aspect ratio */
  object-position: center;
  background: black;
}
</style>
